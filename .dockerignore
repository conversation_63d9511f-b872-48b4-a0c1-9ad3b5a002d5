# Git 相关文件
.git
.gitignore
.gitattributes

# Docker 相关文件
Dockerfile
docker-compose.yml
.dockerignore

# 文档文件
README.md
*.md
docs/

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
.DS_Store
Thumbs.db

# IDE 配置文件
.vscode/
.idea/
*.swp
*.swo
*~

# 编译产物（如果有）
*.o
*.so
*.a

# 配置文件（敏感信息）
*.env
.env.*
config/secrets/

# 测试文件
test/
tests/
*_test.*

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar

# 其他不需要的文件
node_modules/
vendor/
.cache/
