# TrojanServer Docker 环境配置示例
# 复制此文件为 .env 并根据需要修改配置

# 基本配置
COMPOSE_PROJECT_NAME=trojan-server
CONTAINER_NAME=trojan-server-v2
IMAGE_NAME=trojan-server
IMAGE_TAG=v2.0

# 端口配置
HTTP_PORT=8080
HTTPS_PORT=8443
ADMIN_PORT=9090

# 时区配置
TZ=Asia/Shanghai

# 日志配置
LOG_LEVEL=INFO
LOG_MAX_SIZE=100m
LOG_MAX_FILES=10

# 资源限制
CPU_LIMIT=2.0
CPU_RESERVATION=0.5
MEMORY_LIMIT=1g
MEMORY_RESERVATION=256m

# 网络配置
NETWORK_SUBNET=172.20.0.0/16

# 数据目录配置
LOGS_DIR=./logs
CONFIG_DIR=./config
DATA_DIR=./data

# 安全配置
# 设置为 true 启用只读根文件系统
READ_ONLY_ROOT=false

# 健康检查配置
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3
HEALTH_CHECK_START_PERIOD=40s

# 重启策略
RESTART_POLICY=unless-stopped
