# 使用轻量级的 Alpine Linux 作为基础镜像
FROM alpine:latest

# 设置维护者信息
LABEL maintainer="<EMAIL>"
LABEL description="防勒索病毒模拟演练平台 TrojanServer"
LABEL version="2.0"

# 安装必要的运行时依赖
RUN apk update && \
    apk add --no-cache \
    ca-certificates \
    tzdata && \
    rm -rf /var/cache/apk/*

# 设置时区为中国标准时间
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建应用目录
WORKDIR /app

# 创建非 root 用户来运行应用
RUN addgroup -g 1000 appgroup && \
    adduser -D -s /bin/sh -u 1000 -G appgroup appuser

# 复制可执行文件到容器
COPY Server_V2.0 /app/server

# 设置可执行权限
RUN chmod +x /app/server && \
    chown appuser:appgroup /app/server

# 创建日志目录
RUN mkdir -p /app/logs && \
    chown -R appuser:appgroup /app/logs

# 暴露端口（根据实际需要调整）
# 常见的服务端口，您可以根据实际情况修改
EXPOSE 8080 8443 9090

# 切换到非 root 用户
USER appuser

# 设置健康检查（可选，根据实际情况调整）
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD pgrep -f server || exit 1

# 启动应用
CMD ["/app/server"]
