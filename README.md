# 防勒索病毒模拟演练平台 TrojanServer Docker 部署

这是防勒索病毒模拟演练平台 TrojanServer V2.0 的 Docker 部署配置。

## 项目结构

```
TrojanServer/
├── Server_V2.0          # Linux 可执行文件
├── Dockerfile           # Docker 镜像构建文件
├── docker-compose.yml   # Docker Compose 配置
├── .dockerignore        # Docker 忽略文件
└── README.md           # 使用说明
```

## 快速开始

### 1. 构建 Docker 镜像

```bash
# 构建镜像
docker build -t trojan-server:v2.0 .

# 或者使用 docker-compose 构建
docker-compose build
```

### 2. 运行容器

#### 使用 Docker 命令运行

```bash
# 基本运行
docker run -d \
  --name trojan-server-v2 \
  -p 8080:8080 \
  -p 8443:8443 \
  -p 9090:9090 \
  trojan-server:v2.0

# 带数据卷挂载的运行
docker run -d \
  --name trojan-server-v2 \
  -p 8080:8080 \
  -p 8443:8443 \
  -p 9090:9090 \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/config:/app/config:ro \
  -v $(pwd)/data:/app/data \
  -e TZ=Asia/Shanghai \
  trojan-server:v2.0
```

#### 使用 Docker Compose 运行（推荐）

```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重启服务
docker-compose restart
```

### 3. 管理容器

```bash
# 查看容器状态
docker-compose ps

# 进入容器
docker-compose exec trojan-server sh

# 查看实时日志
docker-compose logs -f trojan-server

# 重新构建并启动
docker-compose up -d --build
```

## 配置说明

### 端口配置

默认暴露的端口：
- `8080`: HTTP 服务端口
- `8443`: HTTPS 服务端口  
- `9090`: 管理端口

您可以在 `docker-compose.yml` 中修改端口映射：

```yaml
ports:
  - "自定义端口:容器端口"
```

### 数据卷挂载

- `./logs:/app/logs` - 日志文件目录
- `./config:/app/config:ro` - 配置文件目录（只读）
- `./data:/app/data` - 数据文件目录

### 环境变量

- `TZ`: 时区设置，默认为 `Asia/Shanghai`
- `LOG_LEVEL`: 日志级别，默认为 `INFO`

## 安全配置

### 用户权限
- 容器内使用非 root 用户 `appuser` 运行应用
- UID/GID: 1000

### 安全选项
- `no-new-privileges`: 禁止获取新权限
- 可选择启用只读根文件系统

### 资源限制
- CPU 限制: 最大 2 核心，预留 0.5 核心
- 内存限制: 最大 1GB，预留 256MB

## 健康检查

容器配置了健康检查：
- 检查间隔: 30 秒
- 超时时间: 10 秒
- 重试次数: 3 次
- 启动等待: 40 秒

## 故障排除

### 查看容器日志
```bash
docker-compose logs trojan-server
```

### 检查容器状态
```bash
docker-compose ps
docker inspect trojan-server-v2
```

### 进入容器调试
```bash
docker-compose exec trojan-server sh
```

### 重新构建镜像
```bash
docker-compose build --no-cache
docker-compose up -d
```

## 生产环境部署建议

1. **配置文件管理**: 将敏感配置放在外部配置文件中
2. **日志管理**: 配置日志轮转和集中收集
3. **监控**: 集成监控系统（如 Prometheus）
4. **备份**: 定期备份数据目录
5. **网络安全**: 使用防火墙限制端口访问
6. **SSL/TLS**: 配置 HTTPS 证书

## 自定义配置

如果需要修改默认配置，请：

1. 编辑 `docker-compose.yml` 文件
2. 根据需要调整端口、环境变量、数据卷等
3. 重新启动服务：`docker-compose up -d`

## 注意事项

- 确保 Server_V2.0 文件具有执行权限
- 根据实际应用需求调整端口配置
- 生产环境建议使用具体的镜像标签而非 latest
- 定期更新基础镜像以获取安全补丁
