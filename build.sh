#!/bin/bash

# 防勒索病毒模拟演练平台 TrojanServer Docker 构建脚本
# Build script for TrojanServer Docker

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
IMAGE_NAME="trojan-server"
IMAGE_TAG="v2.0"
CONTAINER_NAME="trojan-server-v2"

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查 Docker 是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_warning "docker-compose 未安装，将使用 docker compose"
    fi
}

# 检查必要文件
check_files() {
    print_step "检查必要文件..."
    
    if [ ! -f "Server_V2.0" ]; then
        print_error "Server_V2.0 文件不存在"
        exit 1
    fi
    
    if [ ! -f "Dockerfile" ]; then
        print_error "Dockerfile 不存在"
        exit 1
    fi
    
    print_message "所有必要文件检查完成"
}

# 构建 Docker 镜像
build_image() {
    print_step "构建 Docker 镜像..."
    
    # 确保 Server_V2.0 有执行权限
    chmod +x Server_V2.0
    
    # 构建镜像
    docker build -t ${IMAGE_NAME}:${IMAGE_TAG} .
    
    if [ $? -eq 0 ]; then
        print_message "镜像构建成功: ${IMAGE_NAME}:${IMAGE_TAG}"
    else
        print_error "镜像构建失败"
        exit 1
    fi
}

# 创建必要的目录
create_directories() {
    print_step "创建必要的目录..."
    
    mkdir -p logs
    mkdir -p config
    mkdir -p data
    
    print_message "目录创建完成"
}

# 停止并删除现有容器
cleanup_container() {
    print_step "清理现有容器..."
    
    if docker ps -a | grep -q ${CONTAINER_NAME}; then
        print_warning "发现现有容器，正在停止并删除..."
        docker stop ${CONTAINER_NAME} 2>/dev/null || true
        docker rm ${CONTAINER_NAME} 2>/dev/null || true
        print_message "容器清理完成"
    fi
}

# 使用 docker-compose 启动
start_with_compose() {
    print_step "使用 docker-compose 启动服务..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d
    else
        docker compose up -d
    fi
    
    if [ $? -eq 0 ]; then
        print_message "服务启动成功"
        print_message "可以使用以下命令查看日志: docker-compose logs -f"
    else
        print_error "服务启动失败"
        exit 1
    fi
}

# 直接使用 docker 启动
start_with_docker() {
    print_step "使用 docker 启动容器..."
    
    docker run -d \
        --name ${CONTAINER_NAME} \
        -p 8080:8080 \
        -p 8443:8443 \
        -p 9090:9090 \
        -v $(pwd)/logs:/app/logs \
        -v $(pwd)/config:/app/config:ro \
        -v $(pwd)/data:/app/data \
        -e TZ=Asia/Shanghai \
        --restart unless-stopped \
        ${IMAGE_NAME}:${IMAGE_TAG}
    
    if [ $? -eq 0 ]; then
        print_message "容器启动成功"
        print_message "可以使用以下命令查看日志: docker logs -f ${CONTAINER_NAME}"
    else
        print_error "容器启动失败"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -b, --build    仅构建镜像，不启动容器"
    echo "  -c, --compose  使用 docker-compose 启动（默认）"
    echo "  -d, --docker   使用 docker 命令启动"
    echo "  --cleanup      仅清理现有容器"
    echo ""
    echo "示例:"
    echo "  $0              # 构建镜像并使用 docker-compose 启动"
    echo "  $0 -b           # 仅构建镜像"
    echo "  $0 -d           # 使用 docker 命令启动"
}

# 主函数
main() {
    local build_only=false
    local use_compose=true
    local cleanup_only=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -b|--build)
                build_only=true
                shift
                ;;
            -c|--compose)
                use_compose=true
                shift
                ;;
            -d|--docker)
                use_compose=false
                shift
                ;;
            --cleanup)
                cleanup_only=true
                shift
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_message "开始构建 TrojanServer Docker 镜像..."
    
    # 执行检查
    check_docker
    check_files
    
    if [ "$cleanup_only" = true ]; then
        cleanup_container
        exit 0
    fi
    
    # 创建目录
    create_directories
    
    # 构建镜像
    build_image
    
    if [ "$build_only" = true ]; then
        print_message "仅构建模式，跳过容器启动"
        exit 0
    fi
    
    # 清理现有容器
    cleanup_container
    
    # 启动容器
    if [ "$use_compose" = true ] && [ -f "docker-compose.yml" ]; then
        start_with_compose
    else
        start_with_docker
    fi
    
    print_message "部署完成！"
    echo ""
    print_message "访问地址:"
    print_message "  HTTP:  http://localhost:8080"
    print_message "  HTTPS: https://localhost:8443"
    print_message "  管理:  http://localhost:9090"
}

# 执行主函数
main "$@"
