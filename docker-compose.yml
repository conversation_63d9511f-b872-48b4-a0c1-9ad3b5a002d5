version: '3.8'

services:
  trojan-server:
    build:
      context: .
      dockerfile: Dockerfile
    image: trojan-server:v2.0
    container_name: trojan-server-v2
    restart: unless-stopped
    
    # 端口映射（根据实际需要调整）
    ports:
      - "8080:8080"   # HTTP 端口
      - "8443:8443"   # HTTPS 端口
      - "9090:9090"   # 管理端口
    
    # 环境变量
    environment:
      - TZ=Asia/Shanghai
      - LOG_LEVEL=INFO
    
    # 数据卷挂载
    volumes:
      - ./logs:/app/logs                    # 日志目录
      - ./config:/app/config:ro             # 配置文件（只读）
      - ./data:/app/data                    # 数据目录
    
    # 网络配置
    networks:
      - trojan-network
    
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 256M
    
    # 健康检查
    healthcheck:
      test: ["CMD", "pgrep", "-f", "server"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 安全配置
    security_opt:
      - no-new-privileges:true
    
    # 只读根文件系统（可选，根据应用需求调整）
    # read_only: true
    
    # 临时文件系统（如果启用只读根文件系统）
    # tmpfs:
    #   - /tmp
    #   - /var/tmp

# 网络配置
networks:
  trojan-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  trojan-logs:
    driver: local
  trojan-data:
    driver: local
