#!/bin/bash

# 防勒索病毒模拟演练平台 TrojanServer Docker 管理脚本
# Management script for TrojanServer Docker

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
CONTAINER_NAME="trojan-server-v2"
SERVICE_NAME="trojan-server"

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查 docker-compose 是否可用
check_compose() {
    if command -v docker-compose &> /dev/null; then
        echo "docker-compose"
    elif command -v docker &> /dev/null && docker compose version &> /dev/null; then
        echo "docker compose"
    else
        echo ""
    fi
}

# 启动服务
start_service() {
    print_step "启动 TrojanServer 服务..."
    
    local compose_cmd=$(check_compose)
    if [ -n "$compose_cmd" ] && [ -f "docker-compose.yml" ]; then
        $compose_cmd up -d
        print_message "服务启动成功"
    else
        print_error "未找到 docker-compose 或 docker-compose.yml 文件"
        exit 1
    fi
}

# 停止服务
stop_service() {
    print_step "停止 TrojanServer 服务..."
    
    local compose_cmd=$(check_compose)
    if [ -n "$compose_cmd" ] && [ -f "docker-compose.yml" ]; then
        $compose_cmd down
        print_message "服务停止成功"
    else
        # 尝试直接停止容器
        if docker ps | grep -q $CONTAINER_NAME; then
            docker stop $CONTAINER_NAME
            docker rm $CONTAINER_NAME
            print_message "容器停止成功"
        else
            print_warning "未找到运行中的容器"
        fi
    fi
}

# 重启服务
restart_service() {
    print_step "重启 TrojanServer 服务..."
    
    local compose_cmd=$(check_compose)
    if [ -n "$compose_cmd" ] && [ -f "docker-compose.yml" ]; then
        $compose_cmd restart
        print_message "服务重启成功"
    else
        stop_service
        sleep 2
        start_service
    fi
}

# 查看服务状态
status_service() {
    print_step "查看 TrojanServer 服务状态..."
    
    local compose_cmd=$(check_compose)
    if [ -n "$compose_cmd" ] && [ -f "docker-compose.yml" ]; then
        $compose_cmd ps
    else
        docker ps -a | grep $CONTAINER_NAME || print_warning "未找到相关容器"
    fi
}

# 查看日志
logs_service() {
    local follow_logs=false
    local lines=100
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--follow)
                follow_logs=true
                shift
                ;;
            -n|--lines)
                lines="$2"
                shift 2
                ;;
            *)
                break
                ;;
        esac
    done
    
    print_step "查看 TrojanServer 服务日志..."
    
    local compose_cmd=$(check_compose)
    if [ -n "$compose_cmd" ] && [ -f "docker-compose.yml" ]; then
        if [ "$follow_logs" = true ]; then
            $compose_cmd logs -f --tail=$lines $SERVICE_NAME
        else
            $compose_cmd logs --tail=$lines $SERVICE_NAME
        fi
    else
        if [ "$follow_logs" = true ]; then
            docker logs -f --tail=$lines $CONTAINER_NAME
        else
            docker logs --tail=$lines $CONTAINER_NAME
        fi
    fi
}

# 进入容器
exec_service() {
    print_step "进入 TrojanServer 容器..."
    
    local compose_cmd=$(check_compose)
    if [ -n "$compose_cmd" ] && [ -f "docker-compose.yml" ]; then
        $compose_cmd exec $SERVICE_NAME sh
    else
        docker exec -it $CONTAINER_NAME sh
    fi
}

# 更新服务
update_service() {
    print_step "更新 TrojanServer 服务..."
    
    local compose_cmd=$(check_compose)
    if [ -n "$compose_cmd" ] && [ -f "docker-compose.yml" ]; then
        $compose_cmd build --no-cache
        $compose_cmd up -d
        print_message "服务更新成功"
    else
        print_error "请使用 build.sh 脚本重新构建镜像"
        exit 1
    fi
}

# 清理资源
cleanup_service() {
    print_step "清理 TrojanServer 相关资源..."
    
    local compose_cmd=$(check_compose)
    if [ -n "$compose_cmd" ] && [ -f "docker-compose.yml" ]; then
        $compose_cmd down -v --rmi all
    else
        # 停止并删除容器
        docker stop $CONTAINER_NAME 2>/dev/null || true
        docker rm $CONTAINER_NAME 2>/dev/null || true
        
        # 删除镜像
        docker rmi trojan-server:v2.0 2>/dev/null || true
    fi
    
    print_message "资源清理完成"
}

# 备份数据
backup_data() {
    local backup_dir="backup_$(date +%Y%m%d_%H%M%S)"
    
    print_step "备份 TrojanServer 数据..."
    
    mkdir -p $backup_dir
    
    # 备份日志
    if [ -d "logs" ]; then
        cp -r logs $backup_dir/
        print_message "日志备份完成"
    fi
    
    # 备份数据
    if [ -d "data" ]; then
        cp -r data $backup_dir/
        print_message "数据备份完成"
    fi
    
    # 备份配置
    if [ -d "config" ]; then
        cp -r config $backup_dir/
        print_message "配置备份完成"
    fi
    
    # 创建压缩包
    tar -czf "${backup_dir}.tar.gz" $backup_dir
    rm -rf $backup_dir
    
    print_message "备份完成: ${backup_dir}.tar.gz"
}

# 监控服务
monitor_service() {
    print_step "监控 TrojanServer 服务..."
    
    while true; do
        clear
        echo "=== TrojanServer 服务监控 ==="
        echo "时间: $(date)"
        echo ""
        
        # 显示容器状态
        echo "容器状态:"
        status_service
        echo ""
        
        # 显示资源使用情况
        echo "资源使用:"
        docker stats --no-stream $CONTAINER_NAME 2>/dev/null || echo "容器未运行"
        echo ""
        
        echo "按 Ctrl+C 退出监控"
        sleep 5
    done
}

# 显示帮助信息
show_help() {
    echo "TrojanServer Docker 管理脚本"
    echo ""
    echo "用法: $0 <命令> [选项]"
    echo ""
    echo "命令:"
    echo "  start      启动服务"
    echo "  stop       停止服务"
    echo "  restart    重启服务"
    echo "  status     查看服务状态"
    echo "  logs       查看服务日志"
    echo "  exec       进入容器"
    echo "  update     更新服务"
    echo "  cleanup    清理资源"
    echo "  backup     备份数据"
    echo "  monitor    监控服务"
    echo "  help       显示帮助信息"
    echo ""
    echo "日志选项:"
    echo "  -f, --follow    实时跟踪日志"
    echo "  -n, --lines N   显示最后 N 行日志（默认 100）"
    echo ""
    echo "示例:"
    echo "  $0 start                # 启动服务"
    echo "  $0 logs -f              # 实时查看日志"
    echo "  $0 logs -n 50           # 查看最后 50 行日志"
    echo "  $0 backup               # 备份数据"
}

# 主函数
main() {
    if [ $# -eq 0 ]; then
        show_help
        exit 1
    fi
    
    local command=$1
    shift
    
    case $command in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        status)
            status_service
            ;;
        logs)
            logs_service "$@"
            ;;
        exec)
            exec_service
            ;;
        update)
            update_service
            ;;
        cleanup)
            cleanup_service
            ;;
        backup)
            backup_data
            ;;
        monitor)
            monitor_service
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
